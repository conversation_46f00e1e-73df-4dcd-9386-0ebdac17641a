import discord
from discord.ext import tasks, commands
import a2s

# Remplace par ton token sécurisé
TOKEN = "MTEwNzI3NDY4NTU4ODE4NTEwOA.GYXkEq.gw3Spurmp22F-8XmJ5g-Nj_brbNDdgE2UINkSg"
SERVER_IP = "***********:7781"
QUERY_PORT = 27016   # généralement +1 par rapport au port de jeu
CHANNEL_ID = 1404584533331804271  # ID du canal Discord pour les logs

intents = discord.Intents.default()
bot = commands.Bot(command_prefix="!", intents=intents)

last_status = None
last_players = None

@tasks.loop(seconds=60)
async def check_server():
    global last_status, last_players
    channel = bot.get_channel(CHANNEL_ID)

    try:
        info = a2s.info((SERVER_IP, QUERY_PORT))
        player_count = info.players
        max_players = info.max_players
        status = "online"

        # Mise à jour du rich presence
        await bot.change_presence(activity=discord.Game(
            name=f"{player_count}/{max_players} sur {info.server_name}"
        ))

        # Logs si changement
        if last_status == "offline":
            await channel.send("✅ Le serveur est de nouveau **en ligne**.")

        if last_players is not None and player_count != last_players:
            diff = player_count - last_players
            if diff > 0:
                await channel.send(f"🟢 {diff} joueur(s) **rejoint** le serveur ({player_count}/{max_players}).")
            else:
                await channel.send(f"🔴 {abs(diff)} joueur(s) **quitté** le serveur ({player_count}/{max_players}).")

        last_players = player_count
        last_status = status

    except Exception:
        status = "offline"
        await bot.change_presence(activity=discord.Game(name="Serveur hors ligne ❌"))
        if last_status != "offline":
            await channel.send("❌ Le serveur est **hors ligne**.")
        last_status = "offline"

@bot.event
async def on_ready():
    print(f"✅ Connecté en tant que {bot.user}")
    check_server.start()

bot.run(TOKEN)
