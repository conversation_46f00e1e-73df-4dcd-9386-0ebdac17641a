import discord
from discord.ext import tasks, commands
import a2s
import asyncio
import logging
from datetime import datetime

# Configuration
TOKEN = "MTEwNzI3NDY4NTU4ODE4NTEwOA.GYXkEq.gw3Spurmp22F-8XmJ5g-Nj_brbNDdgE2UINkSg"
SERVER_IP = "***********"
SERVER_PORT = 7781
QUERY_PORT = 27015  # Port de query A2S (généralement port de jeu - 1)
CHANNEL_ID = 1404584533331804271  # ID du canal Discord pour les logs

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration des intents Discord
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="!", intents=intents)

# Variables globales pour le suivi d'état
last_status = None
last_players = None
last_server_name = None
connection_errors = 0
MAX_CONNECTION_ERRORS = 3

@tasks.loop(seconds=60)
async def check_server():
    global last_status, last_players, last_server_name, connection_errors

    # Récupération du channel
    channel = bot.get_channel(CHANNEL_ID)
    if not channel:
        logger.error(f"Canal Discord avec l'ID {CHANNEL_ID} introuvable!")
        return

    try:
        # Tentative de connexion au serveur avec timeout
        logger.info(f"Vérification du serveur {SERVER_IP}:{QUERY_PORT}")

        # Test de plusieurs ports de query possibles
        query_ports = [QUERY_PORT, 27016, SERVER_PORT + 1, SERVER_PORT]
        info = None
        successful_port = None

        for port in query_ports:
            try:
                logger.info(f"Tentative de connexion sur le port {port}")
                info = await asyncio.wait_for(
                    asyncio.to_thread(a2s.info, (SERVER_IP, port)),
                    timeout=5.0
                )
                successful_port = port
                logger.info(f"Connexion réussie sur le port {successful_port}")
                # Mettre à jour le port global pour les prochaines tentatives
                global QUERY_PORT
                QUERY_PORT = successful_port
                break
            except (asyncio.TimeoutError, Exception) as e:
                logger.debug(f"Échec sur le port {port}: {e}")
                continue

        if info is None:
            raise Exception(f"Impossible de se connecter sur les ports testés: {query_ports}")

        player_count = info.player_count if hasattr(info, 'player_count') else info.players
        max_players = info.max_players
        server_name = info.server_name
        status = "online"

        # Reset du compteur d'erreurs en cas de succès
        connection_errors = 0

        logger.info(f"Serveur en ligne: {player_count}/{max_players} joueurs sur {server_name}")

        # Mise à jour du statut Discord
        activity_name = f"{player_count}/{max_players} joueurs sur {server_name}"
        await bot.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.watching,
                name=activity_name
            ),
            status=discord.Status.online
        )

        # Logs des changements d'état
        if last_status == "offline":
            embed = discord.Embed(
                title="🟢 Serveur en ligne",
                description=f"Le serveur **{server_name}** est de nouveau accessible!",
                color=discord.Color.green(),
                timestamp=datetime.now()
            )
            embed.add_field(name="Joueurs connectés", value=f"{player_count}/{max_players}", inline=True)
            embed.add_field(name="Adresse", value=f"{SERVER_IP}:{SERVER_PORT}", inline=True)
            await channel.send(embed=embed)

        # Logs des changements de joueurs
        if last_players is not None and player_count != last_players:
            diff = player_count - last_players

            if diff > 0:
                embed = discord.Embed(
                    title="🟢 Joueur(s) connecté(s)",
                    description=f"{diff} joueur(s) ont rejoint le serveur",
                    color=discord.Color.green(),
                    timestamp=datetime.now()
                )
            else:
                embed = discord.Embed(
                    title="🔴 Joueur(s) déconnecté(s)",
                    description=f"{abs(diff)} joueur(s) ont quitté le serveur",
                    color=discord.Color.red(),
                    timestamp=datetime.now()
                )

            embed.add_field(name="Joueurs actuels", value=f"{player_count}/{max_players}", inline=True)
            embed.add_field(name="Serveur", value=server_name, inline=True)
            await channel.send(embed=embed)

        # Mise à jour des variables de suivi
        last_players = player_count
        last_status = status
        last_server_name = server_name

    except asyncio.TimeoutError:
        logger.warning("Timeout lors de la connexion au serveur")
        await handle_server_offline(channel, "Timeout de connexion")

    except Exception as e:
        logger.error(f"Erreur lors de la vérification du serveur: {e}")
        await handle_server_offline(channel, f"Erreur: {str(e)}")

async def handle_server_offline(channel, error_msg):
    global last_status, connection_errors

    connection_errors += 1
    logger.warning(f"Serveur hors ligne (erreur #{connection_errors}): {error_msg}")

    # Mise à jour du statut Discord
    await bot.change_presence(
        activity=discord.Activity(
            type=discord.ActivityType.watching,
            name="Serveur hors ligne ❌"
        ),
        status=discord.Status.dnd
    )

    # Envoi du message seulement après plusieurs erreurs consécutives
    if last_status != "offline" and connection_errors >= MAX_CONNECTION_ERRORS:
        embed = discord.Embed(
            title="🔴 Serveur hors ligne",
            description=f"Le serveur **{SERVER_IP}:{SERVER_PORT}** est inaccessible",
            color=discord.Color.red(),
            timestamp=datetime.now()
        )
        embed.add_field(name="Raison", value=error_msg, inline=False)
        embed.add_field(name="Tentatives échouées", value=str(connection_errors), inline=True)
        await channel.send(embed=embed)

    last_status = "offline"

@bot.event
async def on_ready():
    logger.info(f"✅ Bot connecté en tant que {bot.user}")
    logger.info(f"📡 Surveillance du serveur {SERVER_IP}:{SERVER_PORT}")
    logger.info(f"📢 Logs envoyés dans le canal ID: {CHANNEL_ID}")

    # Vérification que le canal existe
    channel = bot.get_channel(CHANNEL_ID)
    if channel:
        logger.info(f"✅ Canal Discord trouvé: #{channel.name}")

        # Message de démarrage
        embed = discord.Embed(
            title="🤖 Bot de surveillance démarré",
            description="Surveillance du serveur ARK activée!",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )
        embed.add_field(name="Serveur surveillé", value=f"{SERVER_IP}:{SERVER_PORT}", inline=True)
        embed.add_field(name="Fréquence de vérification", value="60 secondes", inline=True)
        await channel.send(embed=embed)
    else:
        logger.error(f"❌ Canal Discord avec l'ID {CHANNEL_ID} introuvable!")

    # Démarrage de la tâche de surveillance
    if not check_server.is_running():
        check_server.start()

@bot.command(name="status")
async def server_status(ctx):
    """Commande pour vérifier manuellement le statut du serveur"""
    await ctx.send("🔍 Vérification du serveur en cours...")

    try:
        # Test de plusieurs ports comme dans la fonction principale
        query_ports = [QUERY_PORT, 27015, 27016, SERVER_PORT + 1, SERVER_PORT]
        info = None
        successful_port = None

        for port in query_ports:
            try:
                info = await asyncio.wait_for(
                    asyncio.to_thread(a2s.info, (SERVER_IP, port)),
                    timeout=5.0
                )
                successful_port = port
                break
            except Exception:
                continue

        if info is None:
            raise Exception("Aucun port de query ne répond")

        player_count = info.player_count if hasattr(info, 'player_count') else info.players
        max_players = info.max_players
        server_name = info.server_name

        embed = discord.Embed(
            title="📊 Statut du serveur",
            color=discord.Color.green(),
            timestamp=datetime.now()
        )
        embed.add_field(name="Nom du serveur", value=server_name, inline=False)
        embed.add_field(name="Adresse", value=f"{SERVER_IP}:{SERVER_PORT}", inline=True)
        embed.add_field(name="Port de query", value=str(successful_port), inline=True)
        embed.add_field(name="Joueurs", value=f"{player_count}/{max_players}", inline=True)
        embed.add_field(name="Statut", value="🟢 En ligne", inline=True)

        await ctx.send(embed=embed)

    except Exception as e:
        embed = discord.Embed(
            title="📊 Statut du serveur",
            color=discord.Color.red(),
            timestamp=datetime.now()
        )
        embed.add_field(name="Adresse", value=f"{SERVER_IP}:{SERVER_PORT}", inline=True)
        embed.add_field(name="Ports testés", value=", ".join(map(str, query_ports)), inline=True)
        embed.add_field(name="Statut", value="🔴 Hors ligne", inline=True)
        embed.add_field(name="Erreur", value=str(e), inline=False)

        await ctx.send(embed=embed)

@bot.command(name="diagnostic")
async def diagnostic(ctx):
    """Commande pour diagnostiquer les problèmes de connexion"""
    await ctx.send("🔧 Diagnostic en cours...")

    import socket

    # Test de ping basique
    embed = discord.Embed(
        title="🔧 Diagnostic de connexion",
        color=discord.Color.blue(),
        timestamp=datetime.now()
    )

    # Test de résolution DNS
    try:
        import socket
        ip = socket.gethostbyname(SERVER_IP)
        embed.add_field(name="✅ Résolution IP", value=f"{SERVER_IP} → {ip}", inline=False)
    except Exception as e:
        embed.add_field(name="❌ Résolution IP", value=f"Erreur: {e}", inline=False)

    # Test des ports
    ports_to_test = [SERVER_PORT, 27015, 27016, SERVER_PORT + 1]
    port_results = []

    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3)
            sock.connect((SERVER_IP, port))
            port_results.append(f"✅ {port}")
            sock.close()
        except Exception:
            port_results.append(f"❌ {port}")

    embed.add_field(name="Test des ports UDP", value="\n".join(port_results), inline=False)

    # Test A2S spécifique
    a2s_results = []
    for port in [27015, 27016, SERVER_PORT]:
        try:
            info = await asyncio.wait_for(
                asyncio.to_thread(a2s.info, (SERVER_IP, port)),
                timeout=3.0
            )
            a2s_results.append(f"✅ {port}: {info.server_name}")
        except Exception as e:
            a2s_results.append(f"❌ {port}: {str(e)[:50]}")

    embed.add_field(name="Test A2S Query", value="\n".join(a2s_results), inline=False)

    await ctx.send(embed=embed)

@bot.event
async def on_command_error(ctx, error):
    """Gestion des erreurs de commandes"""
    logger.error(f"Erreur de commande: {error}")
    await ctx.send(f"❌ Une erreur s'est produite: {error}")

# Gestion propre de l'arrêt
@bot.event
async def on_disconnect():
    logger.info("🔌 Bot déconnecté")

if __name__ == "__main__":
    try:
        bot.run(TOKEN)
    except KeyboardInterrupt:
        logger.info("🛑 Arrêt du bot demandé par l'utilisateur")
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
    finally:
        logger.info("👋 Bot arrêté")
