# Bot Discord ARK (Serveur Officiel)
Ce bot permet de :
- Afficher en temps réel le nombre de joueurs connectés sur un serveur ARK officiel
- Envoyer des logs dans un canal Discord :
  - Quand le serveur passe offline/online
  - Quand le nombre de joueurs change

## Installation
1. Installer Python 3.10+
2. Installer les dépendances :
   ```bash
   pip install -r requirements.txt
   ```
3. Modifier `bot.py` :
   - Remplacer `TOKEN` par ton token de bot (ne jamais le partager publiquement !)
   - `CHANNEL_ID` est déjà configuré : 1404584533331804271

## Lance<PERSON> le bot
```bash
python bot.py
```
